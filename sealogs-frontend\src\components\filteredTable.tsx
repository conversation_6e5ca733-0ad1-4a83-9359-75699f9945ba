// filteredTable.tsx
'use client'
import dynamic from 'next/dynamic'
import {
    Table,
    TableBody,
    TableHeader,
    TableFooter,
    TableRow,
    TableHead,
    TableCell,
} from '@/components/ui/table'
import { DataTableToolbar } from './data-table-toolbar'
import { DataTablePagination } from './data-table-pagination'
import { useBreakpoints } from './hooks/useBreakpoints'
import * as React from 'react'
import {
    ColumnDef,
    ColumnFiltersState,
    SortingState,
    flexRender,
    getCoreRowModel,
    getFilteredRowModel,
    getPaginationRowModel,
    getSortedRowModel,
    useReactTable,
} from '@tanstack/react-table'
import { Card } from './ui/card'
import { cn } from '@/app/lib/utils'
import Loading from '@/app/loading'

type BreakpointKey =
    | 'tiny'
    | 'small'
    | 'standard'
    | 'phablet'
    | 'tablet-sm'
    | 'tablet-md'
    | 'tablet-lg'
    | 'landscape'
    | 'laptop'
    | 'desktop'

// Extended ColumnDef type with cellAlignment and breakpoint properties
export type ExtendedColumnDef<TData, TValue = unknown> = ColumnDef<
    TData,
    TValue
> & {
    /** Controls the text alignment for this column's header and cells. Defaults to 'center'. Note: 'title' column is always left-aligned. */
    cellAlignment?: 'left' | 'center' | 'right'
    /** Minimum breakpoint at which this column should be visible. Column will be hidden on smaller screens. */
    breakpoint?: BreakpointKey
    /** Maximum breakpoint at which this column should be visible. Column will be hidden on larger screens. */
    showOnlyBelow?: BreakpointKey
    /** Additional CSS classes to apply to the table cell container */
    cellClassName?: string
}

/**
 * Helper function to create columns with proper typing inference
 * Eliminates the need to explicitly type column arrays
 *
 * Example usage with breakpoint:
 * ```tsx
 * const columns = createColumns([
 *   {
 *     accessorKey: 'name',
 *     header: 'Name',
 *   },
 *   {
 *     accessorKey: 'email',
 *     header: 'Email',
 *     breakpoint: 'tablet-md', // Hide on screens smaller than tablet-md (768px)
 *   },
 *   {
 *     accessorKey: 'phone',
 *     header: 'Phone',
 *     breakpoint: 'laptop', // Hide on screens smaller than laptop (1280px)
 *   },
 *   {
 *     accessorKey: 'mobile',
 *     header: 'Mobile Info',
 *     showOnlyBelow: 'landscape', // Show only on screens smaller than landscape (1024px)
 *   },
 * ])
 * ```
 */
export function createColumns<TData = any>(
    columns: ExtendedColumnDef<TData, any>[],
): ExtendedColumnDef<TData, any>[] {
    return columns
}

// Row status types for highlighting
export type RowStatus = 'overdue' | 'upcoming' | 'normal'

// Function type for evaluating row status
export type RowStatusEvaluator<TData> = (rowData: TData) => RowStatus

interface DataTableProps<TData, TValue> {
    columns: ExtendedColumnDef<TData, TValue>[]
    data: TData[]
    showToolbar?: boolean
    className?: string
    pageSize?: number
    isLoading?: boolean
    pageSizeOptions?: number[]
    showPageSizeSelector?: boolean
    noDataText?: string
    onChange?: any
    /** Optional function to evaluate row status for highlighting. Returns 'overdue', 'upcoming', or 'normal' */
    rowStatus?: RowStatusEvaluator<TData>
    /** Optional onClick handler for report generation or filter application */
    onFilterClick?: () => void
    /** Optional additional props to pass to the Filter component */
    filterProps?: Record<string, any>
}

// Helper function to get alignment classes based on cellAlignment prop
const getAlignmentClasses = (alignment: 'left' | 'center' | 'right') => {
    switch (alignment) {
        case 'left':
            return 'items-left justify-start justify-items-start text-left'
        case 'right':
            return 'items-right justify-end justify-items-end text-right'
        case 'center':
        default:
            return 'items-center justify-center justify-items-center text-center'
    }
}

// Helper function to get row status background classes
const getRowStatusClasses = (status: RowStatus) => {
    switch (status) {
        case 'overdue':
            return 'w-full'
        case 'upcoming':
            return 'w-full'
        case 'normal':
        default:
            return ''
    }
}

// Helper function to get status overlay color
const getStatusOverlayColor = (
    status: RowStatus,
): 'destructive' | 'warning' | undefined => {
    switch (status) {
        case 'overdue':
            return 'destructive'
        case 'upcoming':
            return 'warning'
        case 'normal':
        default:
            return undefined
    }
}

export function DataTable<TData, TValue>({
    columns,
    data,
    showToolbar = true,
    className,
    pageSize = 10,
    pageSizeOptions = [10, 20, 30, 40, 50],
    showPageSizeSelector = true,
    isLoading,
    onChange,
    rowStatus,
    onFilterClick,
    filterProps = {},
    noDataText = 'No results',
}: DataTableProps<TData, TValue>) {
    const [sorting, setSorting] = React.useState<SortingState>([])
    const [columnFilters, setColumnFilters] =
        React.useState<ColumnFiltersState>([])
    const [pagination, setPagination] = React.useState({
        pageIndex: 0,
        pageSize: pageSize,
    })

    // Get current breakpoint states
    const breakpoints = useBreakpoints()

    // Filter columns based on breakpoint visibility
    const visibleColumns = React.useMemo(() => {
        return columns.filter((column) => {
            const extendedColumn = column as ExtendedColumnDef<TData, TValue>

            // Handle showOnlyBelow breakpoint (show only on smaller screens)
            if (extendedColumn.showOnlyBelow) {
                return !breakpoints[extendedColumn.showOnlyBelow]
            }

            // Handle regular breakpoint (show only on larger screens)
            if (extendedColumn.breakpoint) {
                return breakpoints[extendedColumn.breakpoint]
            }

            // If no breakpoint is specified, column is always visible
            return true
        })
    }, [columns, breakpoints])

    // Update pagination when pageSize prop changes
    React.useEffect(() => {
        setPagination((prev) => ({
            ...prev,
            pageSize: pageSize,
        }))
    }, [pageSize])

    const table = useReactTable({
        data,
        columns: visibleColumns,
        onSortingChange: setSorting,
        getCoreRowModel: getCoreRowModel(),
        getPaginationRowModel: getPaginationRowModel(),
        getSortedRowModel: getSortedRowModel(),
        onColumnFiltersChange: setColumnFilters,
        getFilteredRowModel: getFilteredRowModel(),
        onPaginationChange: setPagination,
        state: {
            sorting,
            columnFilters,
            pagination,
        },
    })

    return (
        <div className="space-y-4 pb-8">
            {showToolbar && (
                <Card className="p-0 xs:p-2 md:p-auto">
                    <DataTableToolbar
                        table={table}
                        onChange={onChange}
                        onClick={onFilterClick}
                        filterProps={filterProps}
                    />
                </Card>
            )}
            {isLoading ? (
                <Loading className="h-full" />
            ) : (
                <>
                    <Table
                        className={
                            className ||
                            'p-0 phablet:p-8 lg:p-6 xl:p-8 shadow-none border-0 phablet:border border-border bg-card rounded-lg'
                        }>
                        {table
                            .getHeaderGroups()
                            .some((headerGroup) =>
                                headerGroup.headers.some(
                                    (header) =>
                                        header.column.columnDef.header &&
                                        header.column.columnDef.header !== '',
                                ),
                            ) && (
                            <TableHeader>
                                {table.getHeaderGroups().map((headerGroup) => (
                                    <TableRow
                                        key={headerGroup.id}
                                        noHoverEffect>
                                        {headerGroup.headers.map((header) => {
                                            const columnDef = header.column
                                                .columnDef as ExtendedColumnDef<
                                                TData,
                                                TValue
                                            >
                                            const alignment =
                                                header.column.id === 'title'
                                                    ? 'left'
                                                    : columnDef.cellAlignment ||
                                                      'center'

                                            return (
                                                <TableHead
                                                    key={header.id}
                                                    className={
                                                        header.column.id ===
                                                        'title'
                                                            ? 'items-left justify-items-start text-left'
                                                            : getAlignmentClasses(
                                                                  alignment,
                                                              )
                                                    }>
                                                    {header.isPlaceholder
                                                        ? null
                                                        : flexRender(
                                                              header.column
                                                                  .columnDef
                                                                  .header,
                                                              header.getContext(),
                                                          )}
                                                </TableHead>
                                            )
                                        })}
                                    </TableRow>
                                ))}
                            </TableHeader>
                        )}
                        <TableBody>
                            {table.getRowModel().rows.length ? (
                                table.getRowModel().rows.map((row) => {
                                    // Evaluate row status if rowStatus function is provided
                                    const status = rowStatus
                                        ? rowStatus(row.original)
                                        : 'normal'
                                    const statusClasses =
                                        getRowStatusClasses(status)

                                    return (
                                        <TableRow
                                            key={String(row.id)}
                                            statusOverlayColor={getStatusOverlayColor(
                                                status,
                                            )}
                                            data-state={
                                                row.getIsSelected()
                                                    ? 'selected'
                                                    : undefined
                                            }
                                            className={cn(' ', statusClasses)}>
                                            {row
                                                .getVisibleCells()
                                                .map((cell, i) => {
                                                    const columnDef = cell
                                                        .column
                                                        .columnDef as ExtendedColumnDef<
                                                        TData,
                                                        TValue
                                                    >
                                                    const alignment =
                                                        cell.column.id ===
                                                        'title'
                                                            ? 'left'
                                                            : columnDef.cellAlignment ||
                                                              'center'

                                                    return (
                                                        <TableCell
                                                            key={cell.id}
                                                            statusOverlay={
                                                                status !==
                                                                'normal'
                                                            }
                                                            statusOverlayColor={getStatusOverlayColor(
                                                                status,
                                                            )}
                                                            className={cn(
                                                                '',
                                                                cell.column
                                                                    .id ===
                                                                    'title'
                                                                    ? `${visibleColumns.length > 1 ? 'w-auto' : 'w-full'} items-left px-1.5 xs:px-2.5 justify-items-start text-left`
                                                                    : getAlignmentClasses(
                                                                          alignment,
                                                                      ),
                                                                columnDef.cellClassName,
                                                            )}>
                                                            {flexRender(
                                                                cell.column
                                                                    .columnDef
                                                                    .cell,
                                                                cell.getContext(),
                                                            )}
                                                        </TableCell>
                                                    )
                                                })}
                                        </TableRow>
                                    )
                                })
                            ) : (
                                <TableRow>
                                    <TableCell
                                        colSpan={visibleColumns.length}
                                        className="h-24 text-center">
                                        {noDataText}
                                    </TableCell>
                                </TableRow>
                            )}
                        </TableBody>
                    </Table>

                    {(table.getCanPreviousPage() || table.getCanNextPage()) && (
                        <div className="flex items-center justify-center phablet:justify-end space-x-2 py-4">
                            <DataTablePagination
                                table={table}
                                pageSizeOptions={pageSizeOptions}
                                showPageSizeSelector={showPageSizeSelector}
                            />
                        </div>
                    )}
                </>
            )}
        </div>
    )
}

// Export DataTable as FilteredTable for backward compatibility
export const FilteredTable = DataTable
