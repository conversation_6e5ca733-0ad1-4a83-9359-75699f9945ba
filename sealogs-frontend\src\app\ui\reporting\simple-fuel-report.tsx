'use client'

import { useMemo, useState } from 'react'
import { useLazyQuery } from '@apollo/client'
import { GET_SIMPLE_FUEL_REPORT_ENTRIES } from '@/app/lib/graphQL/query/reporting'
import dayjs from 'dayjs'
import { exportCsv } from '@/app/helpers/csvHelper'
import { exportPdfTable } from '@/app/helpers/pdfHelper'
import { useRouter } from 'next/navigation'
import { ListHeader } from '@/components/ui'
import { createColumns, DataTable } from '@/components/filteredTable'
import { DataTableSortHeader } from '@/components/data-table-sort-header'
import { TableRow, TableCell } from '@/components/ui/table'
import {
    DropdownMenu,
    DropdownMenuContent,
    DropdownMenuItem,
    DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu'
import { useSidebar } from '@/components/ui/sidebar'
import { SealogsCogIcon } from '@/app/lib/icons'

interface DateRange {
    startDate: Date | null
    endDate: Date | null
}

interface IDropdownItem {
    label: string
    value: string
}

interface IReportItem {
    logBookEntryID: number
    vesselID: number
    vesselName: string
    logbookDate: Date
    fuelTankID: number
    fuelTankName: string
    fuelStart: number
    fuelAdded: number
    fuelEnd: number
    fuelUsed: number
    comments?: string
}

// Create columns for the fuel report table
const createFuelReportColumns = () =>
    createColumns<IReportItem>([
        {
            accessorKey: 'vesselName',
            header: ({ column }: { column: any }) => (
                <DataTableSortHeader column={column} title="Vessel" />
            ),
            cellAlignment: 'left' as const,
            cellClassName: 'px-2.5',
            cell: ({ row }: { row: any }) => {
                const item = row.original
                return <div className="font-medium">{item.vesselName}</div>
            },
        },
        {
            accessorKey: 'logbookDate',
            header: ({ column }: { column: any }) => (
                <DataTableSortHeader column={column} title="Log Entry" />
            ),
            cellAlignment: 'left' as const,
            cellClassName: 'px-2.5',
            cell: ({ row }: { row: any }) => {
                const item = row.original
                return <div>{dayjs(item.logbookDate).format('DD/M/YY')}</div>
            },
        },
        {
            accessorKey: 'fuelTankName',
            header: ({ column }: { column: any }) => (
                <DataTableSortHeader column={column} title="Fuel Tank" />
            ),
            cellAlignment: 'left' as const,
            cellClassName: 'px-2.5',
            cell: ({ row }: { row: any }) => {
                const item = row.original
                return <div>{item.fuelTankName}</div>
            },
        },
        {
            accessorKey: 'fuelStart',
            header: ({ column }: { column: any }) => (
                <DataTableSortHeader column={column} title="Fuel Start" />
            ),
            cellAlignment: 'right' as const,
            cellClassName: 'px-2.5',
            cell: ({ row }: { row: any }) => {
                const item = row.original
                return <div>{item.fuelStart.toLocaleString()}</div>
            },
        },
        {
            accessorKey: 'fuelAdded',
            header: ({ column }: { column: any }) => (
                <DataTableSortHeader column={column} title="Fuel Added" />
            ),
            cellAlignment: 'right' as const,
            cellClassName: 'px-2.5',
            cell: ({ row }: { row: any }) => {
                const item = row.original
                return <div>{item.fuelAdded.toLocaleString()}</div>
            },
        },
        {
            accessorKey: 'fuelEnd',
            header: ({ column }: { column: any }) => (
                <DataTableSortHeader column={column} title="Fuel End" />
            ),
            cellAlignment: 'right' as const,
            cellClassName: 'px-2.5',
            cell: ({ row }: { row: any }) => {
                const item = row.original
                return <div>{item.fuelEnd.toLocaleString()}</div>
            },
        },
        {
            accessorKey: 'fuelUsed',
            header: ({ column }: { column: any }) => (
                <DataTableSortHeader column={column} title="Fuel Used" />
            ),
            cellAlignment: 'right' as const,
            cellClassName: 'px-2.5',
            cell: ({ row }: { row: any }) => {
                const item = row.original
                return <div>{item.fuelUsed.toLocaleString()}</div>
            },
        },
        {
            accessorKey: 'comments',
            header: 'Comments',
            cellAlignment: 'left' as const,
            cellClassName: 'px-2.5',
            cell: ({ row }: { row: any }) => {
                const item = row.original
                return <div>{item.comments || '-'}</div>
            },
        },
    ])

// Custom dropdown actions component for the fuel report
function FuelReportFilterActions({
    onDownloadCsv,
    onDownloadPdf,
}: {
    onDownloadCsv: () => void
    onDownloadPdf: () => void
}) {
    const { isMobile } = useSidebar()
    const router = useRouter()

    return (
        <DropdownMenu>
            <DropdownMenuTrigger asChild>
                <SealogsCogIcon size={36} />
            </DropdownMenuTrigger>
            <DropdownMenuContent
                side={isMobile ? 'bottom' : 'right'}
                align={isMobile ? 'end' : 'start'}>
                <div className="text-input flex flex-col items-center justify-center py-[9px]">
                    <DropdownMenuItem
                        variant="backButton"
                        onClick={() => router.push('/reporting')}>
                        Back
                    </DropdownMenuItem>
                    <DropdownMenuItem
                        className="px-[26px]"
                        onClick={onDownloadPdf}>
                        Download PDF
                    </DropdownMenuItem>
                    <DropdownMenuItem
                        className="px-[26px]"
                        onClick={onDownloadCsv}>
                        Download CSV
                    </DropdownMenuItem>
                </div>
            </DropdownMenuContent>
        </DropdownMenu>
    )
}

export default function SimpleFuelReport() {
    const [selectedVessels, setSelectedVessels] = useState<IDropdownItem[]>([])
    const [dateRange, setDateRange] = useState<DateRange>({
        startDate: new Date(),
        endDate: new Date(),
    })

    // Create columns for the table
    const columns = createFuelReportColumns()

    const handleFilterOnChange = ({
        type,
        data,
    }: {
        type: 'dateRange' | 'vessels'
        data: any
    }) => {
        switch (type) {
            case 'dateRange':
                setDateRange(data)
                break

            case 'vessels':
                setSelectedVessels(data)
                break

            default:
                break
        }
    }

    const [getReportData, { called, loading, data }] = useLazyQuery(
        GET_SIMPLE_FUEL_REPORT_ENTRIES,
        {
            fetchPolicy: 'cache-and-network',
            onError: (error: any) => {
                console.error('queryLogBookEntrySections error', error)
            },
        },
    )

    const generateReport = () => {
        const filter: any = {}

        if (dateRange?.startDate !== null && dateRange?.endDate !== null) {
            filter['startDate'] = {
                gte: dateRange.startDate,
                lte: dateRange.endDate,
            }
        }

        if (selectedVessels.length > 0) {
            filter['vehicleID'] = {
                in: selectedVessels.map((v) => v.value),
            }
        }

        getReportData({
            variables: { filter },
        })
    }

    const reportData = useMemo<IReportItem[]>(() => {
        const fetchedData: any = data?.readLogBookEntries?.nodes ?? []

        if (fetchedData.length === 0) {
            return []
        }

        const filteredItems = fetchedData.filter(function (item: any) {
            return item.fuelLog.nodes.length > 0 && item.vehicle.id !== '0'
        })

        if (filteredItems.length === 0) {
            return []
        }

        const items: IReportItem[] = []

        filteredItems.forEach((item: any) => {
            if (item.state !== 'Locked') {
                return
            }

            const logBookDate = new Date(item.startDate)
            const vessel = item.vehicle

            const fuelLogs = item.fuelLog.nodes.filter(
                (item: any) => item.fuelTank.id != 0,
            )

            const fuelTanks = fuelLogs.reduce((acc: any, log: any) => {
                return { ...acc, [log.fuelTank.id]: log.fuelTank.title }
            }, {})

            const logBookEntrySections = item.logBookEntrySections.nodes
            const tripEvents = logBookEntrySections.reduce(
                (acc: any, section: any) => {
                    return [...acc, ...section.tripEvents.nodes]
                },
                [],
            )
            const sectionMemberComments = logBookEntrySections
                .reduce((acc: any, section: any) => {
                    return [...acc, ...section.sectionMemberComments.nodes]
                }, [])
                .map((sectionComment: any) => sectionComment?.comment)
                .filter((value: any) => value != null || value != '')

            for (const key in fuelTanks) {
                if (Object.prototype.hasOwnProperty.call(fuelTanks, key)) {
                    const fuelTankName = fuelTanks[key]

                    const fuelTankLogs = fuelLogs.filter(
                        (item: any) => item.fuelTankID == key,
                    )

                    const fuelLogStart = fuelTankLogs[0]
                    const fuelLogEnd = fuelTankLogs[fuelTankLogs.length - 1]

                    const fuelStart = fuelLogStart?.fuelBefore ?? 0
                    const fuelAdded = calculateFuelAddedFromTripEvents(
                        tripEvents,
                        key,
                    )
                    const fuelEnd = fuelLogEnd?.fuelAfter ?? 0
                    const fuelUsed = fuelStart + fuelAdded - fuelEnd

                    const reportItem: IReportItem = {
                        logBookEntryID: item.id,
                        vesselID: vessel.id,
                        logbookDate: logBookDate,
                        vesselName: vessel.title,
                        fuelTankID: Number(key),
                        fuelTankName: fuelTankName,
                        fuelStart,
                        fuelAdded,
                        fuelEnd,
                        fuelUsed,
                        comments: sectionMemberComments.join(', '),
                    }
                    items.push(reportItem)
                }
            }
        })

        return items
    }, [data, called, loading])

    const downloadCsv = () => {
        if (reportData.length === 0) {
            return
        }

        const csvEntries = []

        csvEntries.push([
            'vessel',
            'log entry',
            'fuel tank',
            'fuel start',
            'fuel added',
            'fuel end',
            'fuel used',
            'comments',
        ])
        reportData.forEach((item) => {
            csvEntries.push([
                item.vesselName,
                item.logbookDate.toISOString(),
                item.fuelTankName,
                item.fuelStart,
                item.fuelAdded,
                item.fuelEnd,
                item.fuelUsed,
                item.comments ?? '',
            ])
        })

        exportCsv(csvEntries)
    }
    const downloadPdf = () => {
        if (reportData.length === 0) {
            return
        }

        const data: any = reportData.map(function (item) {
            return [
                item.vesselName + '',
                dayjs(item.logbookDate).format('DD/MM/YY') + '',
                item.fuelTankName + '',
                item.fuelStart.toLocaleString(),
                item.fuelAdded.toLocaleString(),
                item.fuelEnd.toLocaleString(),
                item.fuelUsed.toLocaleString(),
                `${item.comments ?? ''} `,
            ]
        })

        const totalUsedFuel = reportData.reduce<number>(
            (acc, current) => acc + current.fuelUsed,
            0,
        )

        exportPdfTable({
            headers: [
                [
                    { content: 'Vessel' },
                    { content: 'Log Entry' },
                    { content: 'Fuel Tank' },
                    { content: 'Fuel Start' },
                    { content: 'Fuel Added' },
                    { content: 'Fuel End' },
                    { content: 'Fuel Used' },
                    {
                        content: 'Comments',
                        styles: {
                            cellWidth: 60,
                        },
                    },
                ],
            ],
            body: data,
            footers: [
                [
                    {
                        colSpan: 6,
                        content: 'Total Fuel Used',
                    },
                    {
                        content: totalUsedFuel.toLocaleString(),
                    },
                    {
                        content: '',
                    },
                ],
            ],
            userOptions: {
                showFoot: 'lastPage',
            },
        })
    }

    // Calculate total fuel used for footer
    const totalFuelUsed = useMemo<number>(() => {
        return reportData.reduce<number>(
            (acc, current) => current.fuelUsed + acc,
            0,
        )
    }, [reportData])

    // Create footer content for the table
    const footerContent =
        reportData.length > 0 ? (
            <TableRow className="group border-b">
                <TableCell
                    className="px-2.5 py-3 text-left font-medium"
                    colSpan={6}>
                    Total Fuel Used
                </TableCell>
                <TableCell className="px-2.5 py-3 text-right font-medium">
                    {totalFuelUsed.toLocaleString()}
                </TableCell>
                <TableCell className="px-2.5 py-3"></TableCell>
            </TableRow>
        ) : null

    return (
        <>
            <ListHeader
                title="Simple Fuel Report"
                actions={
                    <FuelReportFilterActions
                        onDownloadCsv={downloadCsv}
                        onDownloadPdf={downloadPdf}
                    />
                }
            />
            <div className="mt-16">
                <DataTable
                    columns={columns}
                    data={reportData}
                    isLoading={called && loading}
                    onChange={handleFilterOnChange}
                    onFilterClick={generateReport}
                    noDataText="No fuel data found, try clicking generate report to view results"
                    showToolbar={true}
                    footer={footerContent}
                />
            </div>
        </>
    )
}

function getFuelAddedByFuelTankID(fuelLogs: any, fuelTankID: any) {
    if (fuelLogs.length === 0) {
        return 0
    }

    const fuelTankLogs = fuelLogs.filter(
        (log: any) => log.fuelTankID == fuelTankID,
    )

    return fuelTankLogs.reduce(
        (acc: number, log: any) => acc + log.fuelAdded,
        0,
    )
}

function calculateFuelAddedFromTripEvents(
    tripEvents: any,
    fuelTankID: any,
): number {
    const fuelAddedLogs: number[] = tripEvents.map(function (tripEvent: any) {
        if (tripEvent.eventCategory === 'RefuellingBunkering') {
            const fuelLogs =
                tripEvent.eventType_RefuellingBunkering.fuelLog.nodes

            return getFuelAddedByFuelTankID(fuelLogs, fuelTankID)
        }

        if (tripEvent.eventCategory === 'Tasking') {
            const fuelLogs = tripEvent.eventType_Tasking.fuelLog.nodes

            return getFuelAddedByFuelTankID(fuelLogs, fuelTankID)
        }

        if (tripEvent.eventCategory === 'PassengerDropFacility') {
            const fuelLogs =
                tripEvent.eventType_PassengerDropFacility.fuelLog.nodes

            return getFuelAddedByFuelTankID(fuelLogs, fuelTankID)
        }

        return 0
    })

    return fuelAddedLogs.reduce((acc, val) => acc + val, 0)
}
